// Etsy-specific product data extractor

window.EtsyExtractor = {
  // Etsy-specific selectors
  selectors: {
    title: [
      'h1[data-test-id="listing-page-title"]',
      'h1.listing-page-title',
      'h1[data-testid="listing-page-title"]',
      'h1', // Fallback to any h1
      '[data-test-id="listing-page-title"]'
    ],
    images: [
      'img[data-testid="listing-page-image"]',
      'img[data-test-id="listing-page-image"]',
      '.listing-page-image img',
      '.carousel-image img',
      '.listing-image img',
      'img[src*="etsystatic.com"]', // Etsy-specific image URLs
      'img[alt*="image"]' // Images with "image" in alt text
    ],
    seller: [
      'a[data-testid="shop-name-link"]',
      'a[data-test-id="shop-name-link"]',
      '.shop-name a',
      '.shop-info a',
      'a[href*="/shop/"]' // Links to shop pages
    ],
    price: [
      '.currency-value',
      '.currency-symbol + .currency-value',
      '[data-testid="price"] .currency-value',
      '.notranslate',
      '[class*="price"]' // Any element with "price" in class name
    ],
    rating: [
      '[data-testid="reviews-summary"] [data-testid="rating"]',
      '.shop-rating .rating',
      '.stars-rating',
      '[class*="rating"]'
    ],
    reviewCount: [
      '[data-testid="reviews-summary"] a',
      '.review-count',
      '.reviews-count'
    ],
    description: [
      '[data-testid="description-text"]',
      '.listing-page-description',
      '.description-text',
      'div[class*="description"]'
    ],
    availability: [
      '[data-testid="quantity-select"]',
      '.quantity-select',
      '.inventory-quantity'
    ]
  },

  // Extract product data from Etsy listing page
  extractProductData() {
    try {
      console.log('🔍 Starting Etsy product data extraction...');
      console.log('Current URL:', window.location.href);
      console.log('Is product page:', this.isProductPage());

      if (!this.isProductPage()) {
        throw new Error('Not on an Etsy product page');
      }

      const productData = {
        title: this.extractTitle(),
        productUrl: CommonExtractor.getCurrentUrl(),
        marketplace: 'etsy',
        sellerName: this.extractSeller(),
        images: this.extractImages(),
        metadata: this.extractMetadata()
      };

      console.log('📦 Extracted product data:', productData);

      // Validate the extracted data
      const validation = CommonExtractor.validateProductData(productData);

      if (!validation.isValid) {
        console.error('❌ Etsy product data validation failed:', validation.errors);
        const errorMessage = 'Failed to extract complete product data: ' + validation.errors.join(', ');
        CommonExtractor.showNotification(errorMessage, 'error');
        throw new Error(errorMessage);
      }

      console.log('✅ Etsy product data extracted and validated successfully');
      return productData;

    } catch (error) {
      console.error('❌ Error extracting Etsy product data:', error);
      const errorMessage = 'Error extracting product data: ' + error.message;
      CommonExtractor.showNotification(errorMessage, 'error');
      throw error; // Re-throw the error instead of returning null
    }
  },

  extractTitle() {
    console.log('Extracting title with selectors:', this.selectors.title);

    for (const selector of this.selectors.title) {
      const title = CommonExtractor.extractText(selector);
      console.log(`Selector "${selector}" returned:`, title);
      if (title) {
        return CommonExtractor.cleanText(title);
      }
    }

    // Fallback to page title
    const pageTitle = document.title;
    console.log('Page title:', pageTitle);
    if (pageTitle && !pageTitle.includes('Etsy')) {
      const cleanTitle = CommonExtractor.cleanText(pageTitle.split('|')[0].split('-')[0]);
      console.log('Cleaned page title:', cleanTitle);
      return cleanTitle;
    }

    // Last resort: look for any h1 element
    const h1Elements = document.querySelectorAll('h1');
    console.log('Found h1 elements:', h1Elements.length);
    for (const h1 of h1Elements) {
      const text = h1.textContent.trim();
      console.log('H1 text:', text);
      if (text && text.length > 3) {
        return CommonExtractor.cleanText(text);
      }
    }

    throw new Error('Could not extract product title');
  },

  extractSeller() {
    for (const selector of this.selectors.seller) {
      const seller = CommonExtractor.extractText(selector);
      if (seller) {
        return CommonExtractor.cleanText(seller);
      }
    }

    return null;
  },

  extractImages() {
    console.log('Extracting images with selectors:', this.selectors.images);
    const images = CommonExtractor.extractImages(this.selectors.images);
    console.log('Found images:', images.length);

    // If no images found with selectors, try broader search
    if (images.length === 0) {
      console.log('No images found with selectors, trying broader search...');
      const allImages = document.querySelectorAll('img');
      console.log('Total images on page:', allImages.length);

      const etsyImages = [];
      allImages.forEach((img, index) => {
        const src = img.src || img.getAttribute('data-src') || img.getAttribute('data-lazy-src');
        if (src && src.includes('etsystatic.com') && !src.includes('avatar') && !src.includes('icon')) {
          console.log('Found Etsy image:', src);
          etsyImages.push({
            imageUrl: src,
            isPrimary: index === 0,
            sortOrder: etsyImages.length
          });
        }
      });

      if (etsyImages.length > 0) {
        return this.processEtsyImages(etsyImages);
      }
    }

    return this.processEtsyImages(images);
  },

  processEtsyImages(images) {
    // Etsy-specific image processing
    return images.map((image, index) => {
      let imageUrl = image.imageUrl;

      // Convert Etsy thumbnail URLs to full size
      if (imageUrl.includes('il_170x135')) {
        imageUrl = imageUrl.replace('il_170x135', 'il_fullxfull');
      } else if (imageUrl.includes('il_340x270')) {
        imageUrl = imageUrl.replace('il_340x270', 'il_fullxfull');
      } else if (imageUrl.includes('il_300x300')) {
        imageUrl = imageUrl.replace('il_300x300', 'il_fullxfull');
      } else if (imageUrl.includes('il_794xN')) {
        // This is already a good size, keep it
      }

      return {
        ...image,
        imageUrl: imageUrl,
        isPrimary: index === 0
      };
    });
  },

  extractMetadata() {
    const metadata = {};

    // Extract price
    const price = this.extractPrice();
    if (price) {
      metadata.price = price.raw;
      metadata.currency = price.currency;
    }

    // Extract rating
    const rating = this.extractRating();
    if (rating) {
      metadata.rating = rating;
    }

    // Extract review count
    const reviewCount = this.extractReviewCount();
    if (reviewCount) {
      metadata.reviewCount = reviewCount;
    }

    // Extract description
    const description = this.extractDescription();
    if (description) {
      metadata.description = description;
    }

    // Extract availability
    const availability = this.extractAvailability();
    if (availability) {
      metadata.availability = availability;
    }

    return metadata;
  },

  extractPrice() {
    // First try the common extractor
    const commonPrice = CommonExtractor.extractPrice(this.selectors.price);
    if (commonPrice) {
      return commonPrice;
    }

    // Etsy-specific price extraction fallbacks
    try {
      // Look for price text patterns in the page
      const pricePatterns = [
        /Price:\s*([₫$€£¥]\s*[\d,]+(?:\.?\d*)?)/i,
        /([₫$€£¥]\s*[\d,]+(?:\.?\d*)?)/,
        /([\d,]+(?:\.?\d*)?[₫$€£¥])/,
        /([\d,]+(?:\.?\d*)?\s*₫)/i // Vietnamese Dong specific
      ];

      const pageText = document.body.textContent;

      for (const pattern of pricePatterns) {
        const match = pageText.match(pattern);
        if (match) {
          const priceText = match[1] || match[0];
          const numericMatch = priceText.match(/[\d,]+\.?\d*/);
          if (numericMatch) {
            return {
              raw: priceText.trim(),
              amount: numericMatch[0].replace(/,/g, ''),
              currency: this.extractCurrencyFromText(priceText)
            };
          }
        }
      }

      // Look for specific elements that might contain price
      const priceElements = document.querySelectorAll('*');
      for (const element of priceElements) {
        const text = element.textContent.trim();
        if (text.match(/^[₫$€£¥]\s*[\d,]+(?:\.?\d*)?$/) || text.match(/^[\d,]+(?:\.?\d*)?[₫$€£¥]$/)) {
          const numericMatch = text.match(/[\d,]+\.?\d*/);
          if (numericMatch) {
            return {
              raw: text,
              amount: numericMatch[0].replace(/,/g, ''),
              currency: this.extractCurrencyFromText(text)
            };
          }
        }
      }
    } catch (error) {
      console.warn('Error in Etsy-specific price extraction:', error);
    }

    return null;
  },

  extractCurrencyFromText(text) {
    if (text.includes('₫')) return 'VND';
    if (text.includes('$')) return 'USD';
    if (text.includes('€')) return 'EUR';
    if (text.includes('£')) return 'GBP';
    if (text.includes('¥')) return 'JPY';
    return 'USD'; // Default
  },

  extractRating() {
    // First try the common extractor
    for (const selector of this.selectors.rating) {
      const rating = CommonExtractor.extractRating([selector]);
      if (rating) {
        return rating;
      }
    }

    // Etsy-specific rating extraction
    try {
      // Look for "X out of 5 stars" pattern in page text
      const pageText = document.body.textContent;
      const ratingMatch = pageText.match(/(\d+(?:\.\d+)?)\s*out\s*of\s*5\s*stars/i);
      if (ratingMatch) {
        return ratingMatch[1];
      }

      // Look for star rating patterns
      const starMatch = pageText.match(/(\d+(?:\.\d+)?)\s*stars?/i);
      if (starMatch) {
        const rating = parseFloat(starMatch[1]);
        if (rating >= 0 && rating <= 5) {
          return starMatch[1];
        }
      }
    } catch (error) {
      console.warn('Error in Etsy-specific rating extraction:', error);
    }

    return null;
  },

  extractReviewCount() {
    // First try the common selectors
    for (const selector of this.selectors.reviewCount) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const text = element.textContent.trim();
          const match = text.match(/(\d+(?:,\d+)*)/);
          if (match) {
            return match[1];
          }
        }
      } catch (error) {
        console.warn(`Failed to extract review count from ${selector}:`, error);
      }
    }

    // Etsy-specific review count extraction
    try {
      const pageText = document.body.textContent;

      // Look for patterns like "(22k reviews)" or "22,000 reviews"
      const reviewPatterns = [
        /\((\d+(?:,\d+)*k?)\s*reviews?\)/i,
        /(\d+(?:,\d+)*k?)\s*reviews?/i,
        /(\d+(?:,\d+)*)\s*review/i
      ];

      for (const pattern of reviewPatterns) {
        const match = pageText.match(pattern);
        if (match) {
          let count = match[1];
          // Convert "22k" to "22000"
          if (count.includes('k')) {
            count = count.replace('k', '000');
          }
          return count;
        }
      }
    } catch (error) {
      console.warn('Error in Etsy-specific review count extraction:', error);
    }

    return null;
  },

  extractDescription() {
    // First try the common selectors
    for (const selector of this.selectors.description) {
      const description = CommonExtractor.extractText(selector);
      if (description && description.length > 10) {
        return CommonExtractor.cleanText(description).substring(0, 500);
      }
    }

    // Etsy-specific description extraction
    try {
      const pageText = document.body.textContent;

      // Look for common Etsy description patterns
      const descriptionPatterns = [
        /This is a digital download[^.]*\./i,
        /Each digital design comes[^.]*\./i,
        /Included:[^.]*\./i,
        /zipped folder containing[^.]*\./i
      ];

      for (const pattern of descriptionPatterns) {
        const match = pageText.match(pattern);
        if (match) {
          return CommonExtractor.cleanText(match[0]).substring(0, 500);
        }
      }

      // Look for text blocks that might be descriptions
      const textElements = document.querySelectorAll('p, div');
      for (const element of textElements) {
        const text = element.textContent.trim();
        if (text.length > 50 && text.length < 1000 &&
            (text.includes('svg') || text.includes('digital') || text.includes('download'))) {
          return CommonExtractor.cleanText(text).substring(0, 500);
        }
      }
    } catch (error) {
      console.warn('Error in Etsy-specific description extraction:', error);
    }

    return null;
  },

  extractAvailability() {
    // First try the common selectors
    for (const selector of this.selectors.availability) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          // Check if quantity selector exists and is not disabled
          if (element.tagName === 'SELECT' && !element.disabled) {
            return 'In Stock';
          }

          const text = element.textContent.trim().toLowerCase();
          if (text.includes('in stock') || text.includes('available')) {
            return 'In Stock';
          } else if (text.includes('out of stock') || text.includes('sold out')) {
            return 'Out of Stock';
          }
        }
      } catch (error) {
        console.warn(`Failed to extract availability from ${selector}:`, error);
      }
    }

    // Etsy-specific availability extraction
    try {
      const pageText = document.body.textContent.toLowerCase();

      // Check for digital download indicators
      if (pageText.includes('instant download') ||
          pageText.includes('digital download') ||
          pageText.includes('your files will be available to download')) {
        return 'Digital Download';
      }

      // Check for add to cart button
      const addToCartButton = document.querySelector('button[type="submit"]');
      if (addToCartButton && !addToCartButton.disabled) {
        const buttonText = addToCartButton.textContent.toLowerCase();
        if (buttonText.includes('add to cart') || buttonText.includes('buy')) {
          return 'In Stock';
        }
      }

      // Check for out of stock indicators
      if (pageText.includes('sold out') || pageText.includes('out of stock')) {
        return 'Out of Stock';
      }
    } catch (error) {
      console.warn('Error in Etsy-specific availability extraction:', error);
    }

    return 'Available'; // Default for Etsy listings
  },

  // Debug method to test extraction on current page
  debugExtraction() {
    console.log('=== Etsy Extractor Debug ===');
    console.log('URL:', window.location.href);
    console.log('Is product page:', this.isProductPage());

    try {
      console.log('\n--- Title Extraction ---');
      const title = this.extractTitle();
      console.log('Title:', title);
    } catch (error) {
      console.error('Title extraction failed:', error);
    }

    try {
      console.log('\n--- Seller Extraction ---');
      const seller = this.extractSeller();
      console.log('Seller:', seller);
    } catch (error) {
      console.error('Seller extraction failed:', error);
    }

    try {
      console.log('\n--- Images Extraction ---');
      const images = this.extractImages();
      console.log('Images:', images);
    } catch (error) {
      console.error('Images extraction failed:', error);
    }

    try {
      console.log('\n--- Price Extraction ---');
      const price = this.extractPrice();
      console.log('Price:', price);
    } catch (error) {
      console.error('Price extraction failed:', error);
    }

    try {
      console.log('\n--- Rating Extraction ---');
      const rating = this.extractRating();
      console.log('Rating:', rating);
    } catch (error) {
      console.error('Rating extraction failed:', error);
    }

    try {
      console.log('\n--- Review Count Extraction ---');
      const reviewCount = this.extractReviewCount();
      console.log('Review Count:', reviewCount);
    } catch (error) {
      console.error('Review count extraction failed:', error);
    }

    try {
      console.log('\n--- Description Extraction ---');
      const description = this.extractDescription();
      console.log('Description:', description);
    } catch (error) {
      console.error('Description extraction failed:', error);
    }

    try {
      console.log('\n--- Availability Extraction ---');
      const availability = this.extractAvailability();
      console.log('Availability:', availability);
    } catch (error) {
      console.error('Availability extraction failed:', error);
    }

    console.log('\n=== End Debug ===');
  },

  // Check if current page is an Etsy product page
  isProductPage() {
    return window.location.pathname.includes('/listing/') &&
           window.location.hostname.includes('etsy.com');
  }
};

// Auto-extract when page loads (for manual crawling)
if (window.EtsyExtractor && window.EtsyExtractor.isProductPage()) {
  // Wait for page to fully load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        console.log('Etsy product page detected and ready for extraction');
      }, 1000);
    });
  } else {
    console.log('Etsy product page detected and ready for extraction');
  }
}

// Verify the extractor is properly attached to window
console.log('EtsyExtractor attached to window:', typeof window.EtsyExtractor !== 'undefined');
