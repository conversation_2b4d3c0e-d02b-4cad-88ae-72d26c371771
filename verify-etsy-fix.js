// Verification script for Etsy extraction fixes
// Run this in the browser console on the Etsy listing page after loading the extractors

console.log('🔍 Verifying Etsy Extraction Fixes...');
console.log('URL:', window.location.href);

// Check if we're on the correct page
const expectedUrl = 'https://www.etsy.com/listing/1633642699/some-things-are-better-left-unsaid-svg';
const isCorrectPage = window.location.href.includes('1633642699');

if (!isCorrectPage) {
    console.error('❌ Please navigate to the test Etsy listing first:');
    console.error(expectedUrl);
} else {
    console.log('✅ On correct Etsy listing page');
}

// Check if extractors are loaded
if (typeof CommonExtractor === 'undefined') {
    console.error('❌ CommonExtractor not loaded');
} else {
    console.log('✅ CommonExtractor loaded');
}

if (typeof EtsyExtractor === 'undefined') {
    console.error('❌ EtsyExtractor not loaded');
} else {
    console.log('✅ EtsyExtractor loaded');
}

// If extractors are loaded, run the tests
if (typeof EtsyExtractor !== 'undefined' && isCorrectPage) {
    console.log('\n🧪 Running Extraction Tests...');
    
    // Test 1: Title extraction
    console.log('\n1️⃣ Testing Title Extraction:');
    try {
        const title = EtsyExtractor.extractTitle();
        console.log('✅ Title:', title);
        
        const expectedTitleParts = ['some things are better left unsaid', 'svg'];
        const titleLower = title.toLowerCase();
        const hasExpectedContent = expectedTitleParts.some(part => titleLower.includes(part));
        
        if (hasExpectedContent) {
            console.log('✅ Title contains expected content');
        } else {
            console.warn('⚠️ Title may not contain expected content');
        }
    } catch (error) {
        console.error('❌ Title extraction failed:', error.message);
    }
    
    // Test 2: Seller extraction
    console.log('\n2️⃣ Testing Seller Extraction:');
    try {
        const seller = EtsyExtractor.extractSeller();
        console.log('✅ Seller:', seller);
        
        if (seller && seller.toLowerCase().includes('blackcatsmedia')) {
            console.log('✅ Seller matches expected value');
        } else {
            console.warn('⚠️ Seller may not match expected value (BlackCatsMedia)');
        }
    } catch (error) {
        console.error('❌ Seller extraction failed:', error.message);
    }
    
    // Test 3: Price extraction
    console.log('\n3️⃣ Testing Price Extraction:');
    try {
        const price = EtsyExtractor.extractPrice();
        console.log('✅ Price:', price);
        
        if (price && (price.raw.includes('₫') || price.currency === 'VND')) {
            console.log('✅ Price includes Vietnamese Dong currency');
        } else {
            console.warn('⚠️ Price may not include expected VND currency');
        }
    } catch (error) {
        console.error('❌ Price extraction failed:', error.message);
    }
    
    // Test 4: Images extraction
    console.log('\n4️⃣ Testing Images Extraction:');
    try {
        const images = EtsyExtractor.extractImages();
        console.log('✅ Images found:', images.length);
        
        if (images.length > 0) {
            console.log('✅ First image URL:', images[0].imageUrl);
            
            const hasEtsyImages = images.some(img => img.imageUrl.includes('etsystatic.com'));
            if (hasEtsyImages) {
                console.log('✅ Contains Etsy static images');
            } else {
                console.warn('⚠️ No Etsy static images found');
            }
        } else {
            console.warn('⚠️ No images extracted');
        }
    } catch (error) {
        console.error('❌ Images extraction failed:', error.message);
    }
    
    // Test 5: Rating extraction
    console.log('\n5️⃣ Testing Rating Extraction:');
    try {
        const rating = EtsyExtractor.extractRating();
        console.log('✅ Rating:', rating);
        
        if (rating && parseFloat(rating) >= 4.0) {
            console.log('✅ Rating is reasonable (>= 4.0)');
        } else {
            console.warn('⚠️ Rating may be incorrect or missing');
        }
    } catch (error) {
        console.error('❌ Rating extraction failed:', error.message);
    }
    
    // Test 6: Review count extraction
    console.log('\n6️⃣ Testing Review Count Extraction:');
    try {
        const reviewCount = EtsyExtractor.extractReviewCount();
        console.log('✅ Review Count:', reviewCount);
        
        if (reviewCount && (reviewCount.includes('k') || parseInt(reviewCount) > 1000)) {
            console.log('✅ Review count indicates popular item');
        } else {
            console.warn('⚠️ Review count may be low or missing');
        }
    } catch (error) {
        console.error('❌ Review count extraction failed:', error.message);
    }
    
    // Test 7: Description extraction
    console.log('\n7️⃣ Testing Description Extraction:');
    try {
        const description = EtsyExtractor.extractDescription();
        console.log('✅ Description:', description ? description.substring(0, 100) + '...' : 'None');
        
        if (description && (description.toLowerCase().includes('digital') || description.toLowerCase().includes('svg'))) {
            console.log('✅ Description contains expected keywords');
        } else {
            console.warn('⚠️ Description may not contain expected keywords');
        }
    } catch (error) {
        console.error('❌ Description extraction failed:', error.message);
    }
    
    // Test 8: Availability extraction
    console.log('\n8️⃣ Testing Availability Extraction:');
    try {
        const availability = EtsyExtractor.extractAvailability();
        console.log('✅ Availability:', availability);
        
        if (availability && (availability.includes('Digital') || availability.includes('Available'))) {
            console.log('✅ Availability indicates product is available');
        } else {
            console.warn('⚠️ Availability may be incorrect');
        }
    } catch (error) {
        console.error('❌ Availability extraction failed:', error.message);
    }
    
    // Test 9: Full extraction
    console.log('\n9️⃣ Testing Full Product Extraction:');
    try {
        const productData = EtsyExtractor.extractProductData();
        console.log('✅ Full extraction successful!');
        console.log('Product Data:', productData);
        
        // Validate required fields
        const requiredFields = ['title', 'productUrl', 'marketplace', 'images'];
        const missingFields = requiredFields.filter(field => !productData[field] || (Array.isArray(productData[field]) && productData[field].length === 0));
        
        if (missingFields.length === 0) {
            console.log('✅ All required fields present');
        } else {
            console.warn('⚠️ Missing required fields:', missingFields);
        }
        
    } catch (error) {
        console.error('❌ Full extraction failed:', error.message);
    }
    
    console.log('\n🎉 Extraction tests completed!');
    console.log('\nTo run debug mode: EtsyExtractor.debugExtraction()');
    
} else {
    console.log('\n📋 Manual Setup Required:');
    console.log('1. Navigate to: ' + expectedUrl);
    console.log('2. Load CommonExtractor: copy content of common-extractor.js to console');
    console.log('3. Load EtsyExtractor: copy content of etsy-extractor.js to console');
    console.log('4. Run this script again');
}

console.log('\n✨ Verification script completed');
