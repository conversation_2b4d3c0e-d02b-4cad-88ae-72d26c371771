// Debug script to test Chrome extension functionality
// Run this in the browser console on the Etsy listing page

console.log('🔧 Chrome Extension Debug Script');
console.log('Current URL:', window.location.href);
console.log('Timestamp:', new Date().toISOString());

// Check if we're on the correct Etsy page
const isEtsyListing = window.location.hostname.includes('etsy.com') && window.location.pathname.includes('/listing/');
console.log('Is Etsy listing page:', isEtsyListing);

// Check if content scripts are loaded
console.log('\n📦 Checking Content Scripts:');
console.log('CommonExtractor loaded:', typeof window.CommonExtractor !== 'undefined');
console.log('EtsyExtractor loaded:', typeof window.EtsyExtractor !== 'undefined');

if (typeof window.CommonExtractor !== 'undefined') {
    console.log('✅ CommonExtractor methods:', Object.keys(window.CommonExtractor));
}

if (typeof window.EtsyExtractor !== 'undefined') {
    console.log('✅ EtsyExtractor methods:', Object.keys(window.EtsyExtractor));
    console.log('✅ EtsyExtractor.isProductPage():', window.EtsyExtractor.isProductPage());
}

// Test basic DOM elements that the extractor needs
console.log('\n🔍 DOM Element Check:');

// Title elements
const titleElements = document.querySelectorAll('h1');
console.log('H1 elements found:', titleElements.length);
if (titleElements.length > 0) {
    console.log('First H1 text:', titleElements[0].textContent.trim().substring(0, 100));
}

// Image elements
const imageElements = document.querySelectorAll('img[src*="etsystatic.com"]');
console.log('Etsy images found:', imageElements.length);

// Shop link elements
const shopLinks = document.querySelectorAll('a[href*="/shop/"]');
console.log('Shop links found:', shopLinks.length);

// Price elements
const priceElements = document.querySelectorAll('*');
let priceFound = false;
for (const el of priceElements) {
    const text = el.textContent.trim();
    if (text.match(/^\d+,\d+₫$/) || text.match(/^₫\s*\d+,\d+$/)) {
        console.log('Price element found:', text);
        priceFound = true;
        break;
    }
}
if (!priceFound) {
    console.log('No price elements found with VND currency');
}

// Test manual extraction if EtsyExtractor is loaded
if (typeof window.EtsyExtractor !== 'undefined' && isEtsyListing) {
    console.log('\n🧪 Testing Manual Extraction:');
    
    try {
        console.log('Testing extractTitle()...');
        const title = window.EtsyExtractor.extractTitle();
        console.log('✅ Title:', title);
    } catch (error) {
        console.error('❌ Title extraction failed:', error.message);
    }
    
    try {
        console.log('Testing extractSeller()...');
        const seller = window.EtsyExtractor.extractSeller();
        console.log('✅ Seller:', seller);
    } catch (error) {
        console.error('❌ Seller extraction failed:', error.message);
    }
    
    try {
        console.log('Testing extractPrice()...');
        const price = window.EtsyExtractor.extractPrice();
        console.log('✅ Price:', price);
    } catch (error) {
        console.error('❌ Price extraction failed:', error.message);
    }
    
    try {
        console.log('Testing extractImages()...');
        const images = window.EtsyExtractor.extractImages();
        console.log('✅ Images:', images.length, 'found');
    } catch (error) {
        console.error('❌ Images extraction failed:', error.message);
    }
    
    try {
        console.log('Testing full extractProductData()...');
        const productData = window.EtsyExtractor.extractProductData();
        console.log('✅ Full extraction successful!');
        console.log('Product data:', productData);
    } catch (error) {
        console.error('❌ Full extraction failed:', error.message);
    }
}

// Check Chrome extension API availability
console.log('\n🔌 Chrome Extension API Check:');
console.log('chrome object available:', typeof chrome !== 'undefined');
console.log('chrome.runtime available:', typeof chrome?.runtime !== 'undefined');

// Test if we can communicate with the extension
if (typeof chrome?.runtime !== 'undefined') {
    console.log('Testing extension communication...');
    try {
        chrome.runtime.sendMessage({type: 'TEST'}, (response) => {
            console.log('Extension response:', response);
        });
    } catch (error) {
        console.log('Extension communication error:', error.message);
    }
}

console.log('\n✨ Debug script completed');
console.log('If content scripts are not loaded, try reloading the page or reinstalling the extension.');
