<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Etsy Extraction</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Etsy Extractor Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>1. Open the Etsy listing URL in a new tab: 
           <a href="https://www.etsy.com/listing/1633642699/some-things-are-better-left-unsaid-svg?ref=shop_home_active_1&pro=1&sts=1&logging_key=1ecd26def97d85135ba153bc8f26fb0a39aecb2f%3A1633642699" target="_blank">
               Etsy Listing
           </a>
        </p>
        <p>2. Open the browser console (F12)</p>
        <p>3. Copy and paste the content of common-extractor.js into the console</p>
        <p>4. Copy and paste the content of etsy-extractor.js into the console</p>
        <p>5. Run the debug command: <code>EtsyExtractor.debugExtraction()</code></p>
        <p>6. Run the full extraction: <code>EtsyExtractor.extractProductData()</code></p>
    </div>

    <div class="test-section">
        <h2>Expected Results</h2>
        <div class="test-result">
Title: "Some things are better left unsaid svg, I'm gonna say them anyway svg, Funny skeleton svg, Sarcastic Funny svg, Snarky Adult humor svg"
Seller: "BlackCatsMedia"
Price: "46,154₫" (Vietnamese Dong)
Images: Multiple product images from etsystatic.com
Rating: "4.9" (out of 5 stars)
Review Count: "22k" or similar
Description: Text about digital download, SVG files, etc.
Availability: "Digital Download" or "Available"
        </div>
    </div>

    <div class="test-section">
        <h2>Manual Test Buttons</h2>
        <p>Use these buttons if you have the extractors loaded in this page's context:</p>
        <button onclick="testExtraction()">Test Full Extraction</button>
        <button onclick="debugExtraction()">Debug Extraction</button>
        <button onclick="testIndividualMethods()">Test Individual Methods</button>
        
        <div id="results"></div>
    </div>

    <script>
        function testExtraction() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Testing Full Extraction...</h3>';
            
            try {
                if (typeof EtsyExtractor === 'undefined') {
                    throw new Error('EtsyExtractor not loaded. Please load the scripts first.');
                }
                
                const result = EtsyExtractor.extractProductData();
                resultsDiv.innerHTML += `<div class="test-result success">Success: ${JSON.stringify(result, null, 2)}</div>`;
            } catch (error) {
                resultsDiv.innerHTML += `<div class="test-result error">Error: ${error.message}</div>`;
            }
        }

        function debugExtraction() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Running Debug Extraction...</h3>';
            
            try {
                if (typeof EtsyExtractor === 'undefined') {
                    throw new Error('EtsyExtractor not loaded. Please load the scripts first.');
                }
                
                EtsyExtractor.debugExtraction();
                resultsDiv.innerHTML += `<div class="test-result success">Debug completed. Check console for details.</div>`;
            } catch (error) {
                resultsDiv.innerHTML += `<div class="test-result error">Error: ${error.message}</div>`;
            }
        }

        function testIndividualMethods() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Testing Individual Methods...</h3>';
            
            if (typeof EtsyExtractor === 'undefined') {
                resultsDiv.innerHTML += `<div class="test-result error">EtsyExtractor not loaded. Please load the scripts first.</div>`;
                return;
            }

            const methods = [
                'extractTitle',
                'extractSeller', 
                'extractImages',
                'extractPrice',
                'extractRating',
                'extractReviewCount',
                'extractDescription',
                'extractAvailability'
            ];

            methods.forEach(method => {
                try {
                    const result = EtsyExtractor[method]();
                    resultsDiv.innerHTML += `<div class="test-result success">${method}: ${JSON.stringify(result, null, 2)}</div>`;
                } catch (error) {
                    resultsDiv.innerHTML += `<div class="test-result error">${method}: ${error.message}</div>`;
                }
            });
        }

        // Show current page info
        document.addEventListener('DOMContentLoaded', function() {
            const info = `
Current URL: ${window.location.href}
Is Etsy page: ${window.location.hostname.includes('etsy.com')}
Is listing page: ${window.location.pathname.includes('/listing/')}
EtsyExtractor loaded: ${typeof EtsyExtractor !== 'undefined'}
CommonExtractor loaded: ${typeof CommonExtractor !== 'undefined'}
            `;
            
            const infoDiv = document.createElement('div');
            infoDiv.className = 'test-result';
            infoDiv.textContent = info;
            document.body.insertBefore(infoDiv, document.body.firstChild);
        });
    </script>
</body>
</html>
