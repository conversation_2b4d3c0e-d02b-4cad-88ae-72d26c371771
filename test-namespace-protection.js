// Test script để kiểm tra namespace protection
// Chạy script này trong console sau khi reload extension

console.log('🛡️ Testing Namespace Protection');
console.log('==============================');

function checkExtractors() {
  const results = {
    timestamp: new Date().toLocaleTimeString(),
    window: {
      CommonExtractor: typeof window.CommonExtractor,
      EtsyExtractor: typeof window.EtsyExtractor
    },
    namespace: {
      exists: typeof window.TikTokShopCrawler !== 'undefined',
      CommonExtractor: window.TikTokShopCrawler ? typeof window.TikTokShopCrawler.CommonExtractor : 'namespace missing',
      EtsyExtractor: window.TikTokShopCrawler ? typeof window.TikTokShopCrawler.EtsyExtractor : 'namespace missing'
    }
  };
  
  return results;
}

// Check initial state
console.log('\n📊 Initial State:');
const initialState = checkExtractors();
console.table(initialState);

// Test restoration function
window.restoreExtractors = function() {
  console.log('\n🔧 Attempting to restore extractors...');
  
  let restored = false;
  
  if (window.TikTokShopCrawler) {
    if (typeof window.CommonExtractor === 'undefined' && window.TikTokShopCrawler.CommonExtractor) {
      window.CommonExtractor = window.TikTokShopCrawler.CommonExtractor;
      console.log('✅ Restored CommonExtractor from namespace');
      restored = true;
    }
    
    if (typeof window.EtsyExtractor === 'undefined' && window.TikTokShopCrawler.EtsyExtractor) {
      window.EtsyExtractor = window.TikTokShopCrawler.EtsyExtractor;
      console.log('✅ Restored EtsyExtractor from namespace');
      restored = true;
    }
  }
  
  if (!restored) {
    console.log('ℹ️ No restoration needed or namespace not available');
  }
  
  const afterRestore = checkExtractors();
  console.table(afterRestore);
  
  return afterRestore;
};

// Test extraction if available
window.testExtraction = function() {
  console.log('\n🧪 Testing Extraction Functions:');
  
  // Ensure extractors are available
  if (typeof window.CommonExtractor === 'undefined') {
    window.restoreExtractors();
  }
  
  if (typeof window.CommonExtractor !== 'undefined') {
    console.log('✅ CommonExtractor available');
    try {
      const url = window.CommonExtractor.getCurrentUrl();
      console.log('  getCurrentUrl():', url);
    } catch (e) {
      console.log('  ❌ getCurrentUrl() failed:', e.message);
    }
  } else {
    console.log('❌ CommonExtractor not available');
  }
  
  if (typeof window.EtsyExtractor !== 'undefined') {
    console.log('✅ EtsyExtractor available');
    try {
      const isProduct = window.EtsyExtractor.isProductPage();
      console.log('  isProductPage():', isProduct);
      
      if (isProduct) {
        console.log('  Testing extraction methods...');
        
        try {
          const title = window.EtsyExtractor.extractTitle();
          console.log('  ✅ Title:', title ? title.substring(0, 50) + '...' : 'null');
        } catch (e) {
          console.log('  ❌ Title extraction failed:', e.message);
        }
        
        try {
          const images = window.EtsyExtractor.extractImages();
          console.log('  ✅ Images:', images ? images.length + ' found' : 'null');
        } catch (e) {
          console.log('  ❌ Image extraction failed:', e.message);
        }
        
        try {
          const price = window.EtsyExtractor.extractPrice();
          console.log('  ✅ Price:', price ? price.raw : 'null');
        } catch (e) {
          console.log('  ❌ Price extraction failed:', e.message);
        }
      }
    } catch (e) {
      console.log('  ❌ isProductPage() failed:', e.message);
    }
  } else {
    console.log('❌ EtsyExtractor not available');
  }
};

// Monitor changes every 2 seconds for 10 seconds
let monitorCount = 0;
const monitorInterval = setInterval(() => {
  monitorCount++;
  console.log(`\n📊 Monitor Check ${monitorCount}:`);
  const currentState = checkExtractors();
  console.table(currentState);
  
  // Auto-restore if needed
  if ((currentState.window.CommonExtractor === 'undefined' || currentState.window.EtsyExtractor === 'undefined') &&
      currentState.namespace.exists) {
    console.log('🔧 Auto-restoring extractors...');
    window.restoreExtractors();
  }
  
  if (monitorCount >= 5) {
    clearInterval(monitorInterval);
    console.log('\n✅ Monitoring completed');
    console.log('\n📋 Available Commands:');
    console.log('• window.restoreExtractors() - Restore from namespace');
    console.log('• window.testExtraction() - Test extraction functions');
    console.log('• checkExtractors() - Check current state');
  }
}, 2000);

console.log('\n📋 Instructions:');
console.log('1. Wait for monitoring to complete (10 seconds)');
console.log('2. Use window.testExtraction() to test functionality');
console.log('3. Use window.restoreExtractors() if extractors are missing');
console.log('4. Check console for auto-restoration messages');

console.log('\n⏰ Monitoring started...');
