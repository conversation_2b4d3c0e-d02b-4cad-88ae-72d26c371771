// Quick test script to verify extension functionality
// Run this in the browser console on the Etsy listing page

console.log('🚀 Quick Extension Test');
console.log('======================');

// Test 1: Check if we're on the right page
console.log('\n1. Page Check:');
const isEtsy = window.location.hostname.includes('etsy.com');
const isListing = window.location.pathname.includes('/listing/');
const isTargetListing = window.location.href.includes('1633642699');

console.log(`✓ Etsy domain: ${isEtsy}`);
console.log(`✓ Listing page: ${isListing}`);
console.log(`✓ Target listing: ${isTargetListing}`);

if (!isEtsy || !isListing) {
    console.error('❌ Please navigate to the Etsy listing first!');
    console.log('URL: https://www.etsy.com/listing/1633642699/some-things-are-better-left-unsaid-svg');
}

// Test 2: Check content scripts
console.log('\n2. Content Scripts Check:');
const hasCommon = typeof window.CommonExtractor !== 'undefined';
const hasEtsy = typeof window.EtsyExtractor !== 'undefined';

console.log(`✓ CommonExtractor: ${hasCommon ? '✅ Loaded' : '❌ Missing'}`);
console.log(`✓ EtsyExtractor: ${hasEtsy ? '✅ Loaded' : '❌ Missing'}`);

if (!hasCommon || !hasEtsy) {
    console.warn('⚠️ Content scripts not loaded. This could be because:');
    console.warn('  • Extension not installed/enabled');
    console.warn('  • Extension needs to be reloaded');
    console.warn('  • Page needs to be refreshed');
    console.warn('  • Content script injection failed');
}

// Test 3: Basic DOM elements
console.log('\n3. DOM Elements Check:');
const h1Elements = document.querySelectorAll('h1');
const images = document.querySelectorAll('img[src*="etsystatic.com"]');
const shopLinks = document.querySelectorAll('a[href*="/shop/"]');

console.log(`✓ H1 elements: ${h1Elements.length}`);
console.log(`✓ Etsy images: ${images.length}`);
console.log(`✓ Shop links: ${shopLinks.length}`);

if (h1Elements.length > 0) {
    console.log(`  First H1: "${h1Elements[0].textContent.trim().substring(0, 50)}..."`);
}

// Test 4: Price detection
console.log('\n4. Price Detection:');
const pageText = document.body.textContent;
const vndPrices = pageText.match(/\d+,\d+₫/g);
const priceElements = document.querySelectorAll('*');

console.log(`✓ VND prices found in text: ${vndPrices ? vndPrices.length : 0}`);
if (vndPrices && vndPrices.length > 0) {
    console.log(`  Example: ${vndPrices[0]}`);
}

// Test 5: Extension API
console.log('\n5. Extension API Check:');
const hasChromeAPI = typeof chrome !== 'undefined';
const hasRuntime = typeof chrome?.runtime !== 'undefined';

console.log(`✓ Chrome API: ${hasChromeAPI ? '✅ Available' : '❌ Missing'}`);
console.log(`✓ Runtime API: ${hasRuntime ? '✅ Available' : '❌ Missing'}`);

// Test 6: Manual extraction (if scripts are loaded)
if (hasEtsy && isListing) {
    console.log('\n6. Manual Extraction Test:');
    
    try {
        console.log('Testing isProductPage()...');
        const isProduct = window.EtsyExtractor.isProductPage();
        console.log(`✓ Is product page: ${isProduct ? '✅ Yes' : '❌ No'}`);
        
        if (isProduct) {
            console.log('Testing individual extraction methods...');
            
            // Test title
            try {
                const title = window.EtsyExtractor.extractTitle();
                console.log(`✓ Title: ${title ? '✅ Success' : '❌ Failed'}`);
                if (title) console.log(`  "${title.substring(0, 50)}..."`);
            } catch (e) {
                console.log(`✓ Title: ❌ Error - ${e.message}`);
            }
            
            // Test seller
            try {
                const seller = window.EtsyExtractor.extractSeller();
                console.log(`✓ Seller: ${seller ? '✅ Success' : '❌ Failed'}`);
                if (seller) console.log(`  "${seller}"`);
            } catch (e) {
                console.log(`✓ Seller: ❌ Error - ${e.message}`);
            }
            
            // Test images
            try {
                const images = window.EtsyExtractor.extractImages();
                console.log(`✓ Images: ${images && images.length > 0 ? '✅ Success' : '❌ Failed'}`);
                if (images) console.log(`  Found ${images.length} images`);
            } catch (e) {
                console.log(`✓ Images: ❌ Error - ${e.message}`);
            }
            
            // Test price
            try {
                const price = window.EtsyExtractor.extractPrice();
                console.log(`✓ Price: ${price ? '✅ Success' : '❌ Failed'}`);
                if (price) console.log(`  ${price.raw} (${price.currency})`);
            } catch (e) {
                console.log(`✓ Price: ❌ Error - ${e.message}`);
            }
        }
    } catch (error) {
        console.log(`❌ Manual extraction failed: ${error.message}`);
    }
}

// Summary and recommendations
console.log('\n📋 Summary & Recommendations:');
console.log('============================');

if (!isEtsy || !isListing) {
    console.log('❌ Navigate to the correct Etsy listing page first');
} else if (!hasCommon || !hasEtsy) {
    console.log('❌ Content scripts not loaded:');
    console.log('  1. Go to chrome://extensions/');
    console.log('  2. Find "Product Crawler" extension');
    console.log('  3. Click the reload button (🔄)');
    console.log('  4. Refresh this page (F5)');
    console.log('  5. Run this test again');
} else {
    console.log('✅ Extension appears to be working!');
    console.log('  • Try using the extension popup');
    console.log('  • Click the Debug button first');
    console.log('  • Then try Extract Product');
}

console.log('\n🔧 If problems persist:');
console.log('  • Check browser console for errors');
console.log('  • Disable other extensions temporarily');
console.log('  • Try in incognito mode');
console.log('  • Check extension permissions');

console.log('\n✨ Test completed!');
