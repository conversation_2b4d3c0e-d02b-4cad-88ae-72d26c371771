# Etsy Extractor Testing Instructions

## Quick Test (Recommended)

1. **Navigate to the test Etsy listing:**
   ```
   https://www.etsy.com/listing/1633642699/some-things-are-better-left-unsaid-svg?ref=shop_home_active_1&pro=1&sts=1&logging_key=1ecd26def97d85135ba153bc8f26fb0a39aecb2f%3A1633642699
   ```

2. **Open browser console** (F12 → Console tab)

3. **Load the extractors** (copy and paste each file content):
   - First: Copy all content from `content-scripts/common-extractor.js` and paste in console
   - Then: Copy all content from `content-scripts/etsy-extractor.js` and paste in console

4. **Run the verification script:**
   - Copy all content from `verify-etsy-fix.js` and paste in console
   - This will run comprehensive tests and show results

5. **Expected Results:**
   - ✅ Title: "Some things are better left unsaid svg, I'm gonna say them anyway svg..."
   - ✅ Seller: "BlackCatsMedia"
   - ✅ Price: "46,154₫" (Vietnamese Dong)
   - ✅ Images: Multiple product images from etsystatic.com
   - ✅ Rating: "4.9" 
   - ✅ Review Count: "22k" or similar
   - ✅ Description: Text about digital download, SVG files
   - ✅ Availability: "Digital Download" or "Available"

## Chrome Extension Test

1. **Load the extension** in Chrome:
   - Go to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select the `tts-chrome-extension` folder

2. **Navigate to the test Etsy listing** (URL above)

3. **Click the extension icon** in the toolbar

4. **Click "Extract Product"** button

5. **Check console** for any errors or success messages

## Debug Mode

If extraction fails, run debug mode in console:
```javascript
EtsyExtractor.debugExtraction()
```

This will show detailed information about each extraction step.

## Manual Element Inspection

You can also manually test selectors in console:

```javascript
// Test title selectors
document.querySelector('h1')?.textContent

// Test image selectors  
document.querySelectorAll('img[src*="etsystatic.com"]').length

// Test price patterns
document.body.textContent.match(/(\d+,\d+₫)/g)

// Test seller links
document.querySelector('a[href*="/shop/"]')?.textContent
```

## Common Issues and Solutions

### Issue: "EtsyExtractor not loaded"
**Solution:** Make sure to copy and paste the content of both `common-extractor.js` and `etsy-extractor.js` into the console first.

### Issue: "Could not extract product title"
**Solution:** Check if you're on the correct Etsy listing page. The URL should contain `/listing/1633642699/`.

### Issue: "At least one product image is required"
**Solution:** This indicates the image extraction failed. Check the console for more details and ensure the page has fully loaded.

### Issue: Price extraction returns null
**Solution:** The page might be showing a different currency. Check the page text for price patterns manually.

## Files Modified

The following files were updated to fix the extraction issues:

- `content-scripts/etsy-extractor.js` - Main fixes for selectors and extraction logic
- `test-etsy-console.js` - Console test script for DOM analysis
- `test-etsy-extraction.html` - HTML test page
- `verify-etsy-fix.js` - Comprehensive verification script

## Key Improvements Made

1. **Enhanced Selectors:** Added more fallback selectors and Etsy-specific patterns
2. **Better Price Extraction:** Added Vietnamese Dong support and regex patterns
3. **Improved Image Handling:** Better Etsy image URL detection and processing
4. **Enhanced Text Extraction:** Better rating, review count, and description extraction
5. **Debug Tools:** Added comprehensive debugging and testing tools
6. **Availability Detection:** Better detection of digital downloads and product availability

## Success Criteria

The extraction should successfully return a product object with:
- Valid title (non-empty string)
- Product URL (current page URL)
- Marketplace: "etsy"
- At least one image URL
- Seller name
- Price information (amount and currency)
- Additional metadata (rating, reviews, description, availability)

If all tests pass, the "Extraction failed: Failed to extract product data" error should be resolved.
