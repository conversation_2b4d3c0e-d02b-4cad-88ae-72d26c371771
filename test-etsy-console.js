// Console test script for Etsy extraction
// Run this in the browser console on the Etsy listing page

console.log('=== Etsy Extraction Test ===');
console.log('URL:', window.location.href);

// Test if we're on the right page
if (!window.location.hostname.includes('etsy.com')) {
    console.error('❌ Not on Etsy.com');
} else if (!window.location.pathname.includes('/listing/')) {
    console.error('❌ Not on a listing page');
} else {
    console.log('✅ On Etsy listing page');
}

// Test basic DOM elements
console.log('\n=== DOM Analysis ===');

// Check for title elements
const titleSelectors = [
    'h1[data-test-id="listing-page-title"]',
    'h1.listing-page-title', 
    'h1[data-testid="listing-page-title"]',
    'h1'
];

console.log('Title elements:');
titleSelectors.forEach(selector => {
    const element = document.querySelector(selector);
    console.log(`  ${selector}:`, element ? element.textContent.trim() : 'Not found');
});

// Check page title
console.log('Page title:', document.title);

// Check for images
console.log('\nImage elements:');
const imageSelectors = [
    'img[data-testid="listing-page-image"]',
    'img[data-test-id="listing-page-image"]',
    'img[src*="etsystatic.com"]'
];

imageSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log(`  ${selector}: ${elements.length} found`);
    if (elements.length > 0) {
        console.log(`    First image src:`, elements[0].src);
    }
});

// Check for seller
console.log('\nSeller elements:');
const sellerSelectors = [
    'a[data-testid="shop-name-link"]',
    'a[data-test-id="shop-name-link"]',
    'a[href*="/shop/"]'
];

sellerSelectors.forEach(selector => {
    const element = document.querySelector(selector);
    console.log(`  ${selector}:`, element ? element.textContent.trim() : 'Not found');
});

// Check for price
console.log('\nPrice elements:');
const priceSelectors = [
    '.currency-value',
    '.notranslate',
    '[class*="price"]'
];

priceSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log(`  ${selector}: ${elements.length} found`);
    elements.forEach((el, i) => {
        if (i < 3) { // Show first 3
            console.log(`    [${i}]:`, el.textContent.trim());
        }
    });
});

// Look for price in page text
console.log('\nPrice patterns in page text:');
const pageText = document.body.textContent;
const pricePatterns = [
    /Price:\s*([₫$€£¥]\s*[\d,]+(?:\.?\d*)?)/i,
    /([₫$€£¥]\s*[\d,]+(?:\.?\d*)?)/g,
    /([\d,]+(?:\.?\d*)?[₫$€£¥])/g,
    /([\d,]+(?:\.?\d*)?\s*₫)/gi
];

pricePatterns.forEach((pattern, i) => {
    const matches = pageText.match(pattern);
    if (matches) {
        console.log(`  Pattern ${i + 1}:`, matches.slice(0, 5)); // Show first 5 matches
    }
});

// Check for rating
console.log('\nRating elements:');
const ratingText = pageText.match(/(\d+(?:\.\d+)?)\s*out\s*of\s*5\s*stars/i);
if (ratingText) {
    console.log('  Rating found in text:', ratingText[1]);
}

// Check for review count
console.log('\nReview count:');
const reviewMatch = pageText.match(/\((\d+(?:,\d+)*k?)\s*reviews?\)/i);
if (reviewMatch) {
    console.log('  Review count found:', reviewMatch[1]);
}

// Check for description patterns
console.log('\nDescription patterns:');
const descPatterns = [
    /This is a digital download[^.]*\./i,
    /Each digital design comes[^.]*\./i,
    /Included:[^.]*\./i
];

descPatterns.forEach((pattern, i) => {
    const match = pageText.match(pattern);
    if (match) {
        console.log(`  Pattern ${i + 1}:`, match[0]);
    }
});

// Check availability
console.log('\nAvailability indicators:');
const availabilityIndicators = [
    'instant download',
    'digital download', 
    'add to cart',
    'sold out'
];

availabilityIndicators.forEach(indicator => {
    const found = pageText.toLowerCase().includes(indicator);
    console.log(`  "${indicator}": ${found ? '✅' : '❌'}`);
});

console.log('\n=== Test Complete ===');
console.log('If EtsyExtractor is loaded, run: EtsyExtractor.debugExtraction()');
